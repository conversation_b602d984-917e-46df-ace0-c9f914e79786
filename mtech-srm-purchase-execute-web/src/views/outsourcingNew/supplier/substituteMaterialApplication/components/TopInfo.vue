<!-- 替代料申请管理详情页-头部信息组件 -->
<template>
  <div class="top-info">
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div :class="[getStatusClass(headerInfo.status), 'mr20']">
        {{ headerInfo.statusText }}
      </div>
      <div class="infos mr20">
        {{ $t('替代料申请单号：') }}{{ headerInfo.substituteApplicationNo }}
      </div>
      <div class="infos mr20">
        {{ $t('创建人：') }}{{ headerInfo.createUserName }}
      </div>
      <div class="infos">
        {{ $t('创建时间：') }}{{ headerInfo.createTime | dateFormat }}
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <div class="action-buttons">
        <mt-button 
          v-if="isEditMode" 
          css-class="e-flat" 
          :is-primary="false" 
          @click="handleGetMaterialInfo"
        >
          {{ $t('获取物料信息') }}
        </mt-button>
        <mt-button 
          v-if="isEditMode" 
          css-class="e-flat" 
          :is-primary="false" 
          @click="handleSave"
        >
          {{ $t('保存') }}
        </mt-button>
        <mt-button 
          v-if="isEditMode && canSubmit" 
          css-class="e-flat" 
          :is-primary="true" 
          @click="handleSubmit"
        >
          {{ $t('提交') }}
        </mt-button>
        <mt-button 
          css-class="e-flat" 
          :is-primary="false" 
          @click="handleGoBack"
        >
          {{ $t('返回') }}
        </mt-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TopInfo',
  props: {
    // 头部信息
    headerInfo: {
      type: Object,
      default: () => ({})
    },
    // 是否编辑模式
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 是否可以提交
    canSubmit() {
      return this.headerInfo.status === '0' // 待处理状态才能提交
    }
  },
  filters: {
    // 日期格式化
    dateFormat(value) {
      if (!value) return ''
      return value.replace(/T/, ' ').replace(/\.\d{3}Z$/, '')
    }
  },
  methods: {
    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        '0': 'status-pending', // 待处理
        '1': 'status-processed', // 已处理
        '2': 'status-rejected' // 已退回
      }
      return statusClassMap[status] || 'status-default'
    },
    
    // 获取物料信息
    handleGetMaterialInfo() {
      this.$emit('getMaterialInfo')
    },
    
    // 保存
    handleSave() {
      this.$emit('doSave')
    },
    
    // 提交
    handleSubmit() {
      this.$emit('doSubmit')
    },
    
    // 返回
    handleGoBack() {
      this.$emit('goBack')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.infos {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
}

.mr20 {
  margin-right: 20px;
}

.middle-blank {
  flex: 1;
  min-width: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

// 状态样式
.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
}

.status-processed {
  background-color: #f6ffed;
  color: #52c41a;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
}

.status-rejected {
  background-color: #fff2f0;
  color: #ff4d4f;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
}

.status-default {
  background-color: #f5f5f5;
  color: #999;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
}

// 响应式布局
@media (max-width: 768px) {
  .header-box {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .middle-blank {
    display: none;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: flex-start;
  }
  
  .infos {
    margin-right: 0 !important;
    margin-bottom: 5px;
  }
}
</style>
