<!-- 供方-替代料申请管理-详情页 -->
<template>
  <div class="full-height vertical-flex-box detail-page">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      :is-edit-mode="isEditMode"
      @goBack="goBack"
      @doSave="doSave"
      @doSubmit="doSubmit"
      @getMaterialInfo="getMaterialInfo"
    />

    <!-- Tab切换 -->
    <mt-tabs
      class="flex-keep"
      tab-id="substitute-material-tab"
      :e-tab="false"
      :data-source="tabList"
      @handleSelectTab="handleSelectTab"
    />

    <!-- 基本信息 -->
    <div class="flex-fit" v-show="currentTabIndex === 0">
      <div class="detail-content">
        <!-- 详情抬头 -->
        <div class="detail-header">
          <h3 class="section-title">{{ $t('详情抬头') }}</h3>
          <div class="header-form">
            <mt-form ref="headerForm" :model="headerInfo" :rules="headerRules">
              <mt-row>
                <mt-col :span="8">
                  <mt-form-item prop="substituteApplicationNo" :label="$t('替代料申请单号')">
                    <mt-input
                      v-model="headerInfo.substituteApplicationNo"
                      :disabled="true"
                      :placeholder="$t('系统自动生成')"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="8">
                  <mt-form-item prop="status" :label="$t('状态')">
                    <mt-input v-model="headerInfo.statusText" :disabled="true" />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="8">
                  <mt-form-item prop="purchaseOrderNo" :label="$t('采购订单号')">
                    <mt-input v-model="headerInfo.purchaseOrderNo" :disabled="!isEditMode" />
                  </mt-form-item>
                </mt-col>
              </mt-row>
              <mt-row>
                <mt-col :span="8">
                  <mt-form-item prop="purchaseOrderLineNo" :label="$t('采购订单行号')">
                    <mt-input v-model="headerInfo.purchaseOrderLineNo" :disabled="!isEditMode" />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="8">
                  <mt-form-item prop="siteCode" :label="$t('工厂代码')">
                    <mt-select
                      v-model="headerInfo.siteCode"
                      :disabled="!isEditMode"
                      :data-source="siteOptions"
                      text-field="siteName"
                      value-field="siteCode"
                      @change="onSiteChange"
                    />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="8">
                  <mt-form-item prop="siteName" :label="$t('工厂名称')">
                    <mt-input v-model="headerInfo.siteName" :disabled="true" />
                  </mt-form-item>
                </mt-col>
              </mt-row>
              <mt-row>
                <mt-col :span="8">
                  <mt-form-item prop="supplierCode" :label="$t('加工商编码')">
                    <mt-input v-model="headerInfo.supplierCode" :disabled="true" />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="8">
                  <mt-form-item prop="supplierName" :label="$t('加工商名称')">
                    <mt-input v-model="headerInfo.supplierName" :disabled="true" />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="8">
                  <mt-form-item prop="syncStatus" :label="$t('同步状态')">
                    <mt-input v-model="headerInfo.syncStatusText" :disabled="true" />
                  </mt-form-item>
                </mt-col>
              </mt-row>
              <mt-row>
                <mt-col :span="24">
                  <mt-form-item prop="syncMessage" :label="$t('同步接口信息')">
                    <mt-textarea v-model="headerInfo.syncMessage" :disabled="true" :rows="3" />
                  </mt-form-item>
                </mt-col>
              </mt-row>
              <mt-row>
                <mt-col :span="24">
                  <mt-form-item prop="buyerRemark" :label="$t('采方备注')">
                    <mt-textarea v-model="headerInfo.buyerRemark" :disabled="true" :rows="3" />
                  </mt-form-item>
                </mt-col>
              </mt-row>
              <mt-row>
                <mt-col :span="24">
                  <mt-form-item prop="supplierRemark" :label="$t('供方备注')">
                    <mt-textarea
                      v-model="headerInfo.supplierRemark"
                      :disabled="!isEditMode"
                      :rows="3"
                    />
                  </mt-form-item>
                </mt-col>
              </mt-row>
              <mt-row>
                <mt-col :span="8">
                  <mt-form-item prop="createUserName" :label="$t('创建人')">
                    <mt-input v-model="headerInfo.createUserName" :disabled="true" />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="8">
                  <mt-form-item prop="createTime" :label="$t('创建时间')">
                    <mt-input v-model="headerInfo.createTime" :disabled="true" />
                  </mt-form-item>
                </mt-col>
                <mt-col :span="8">
                  <mt-form-item prop="updateUserName" :label="$t('最后更新人')">
                    <mt-input v-model="headerInfo.updateUserName" :disabled="true" />
                  </mt-form-item>
                </mt-col>
              </mt-row>
              <mt-row>
                <mt-col :span="8">
                  <mt-form-item prop="updateTime" :label="$t('最后更新时间')">
                    <mt-input v-model="headerInfo.updateTime" :disabled="true" />
                  </mt-form-item>
                </mt-col>
              </mt-row>
            </mt-form>
          </div>
        </div>

        <!-- 详情物料信息 -->
        <div class="detail-materials">
          <h3 class="section-title">
            {{ $t('详情物料信息') }}
            <div class="section-actions">
              <mt-button v-if="isEditMode" size="small" css-class="e-flat" @click="addMaterialRow">
                {{ $t('新增行') }}
              </mt-button>
              <mt-button
                v-if="isEditMode"
                size="small"
                css-class="e-flat"
                @click="deleteMaterialRows"
              >
                {{ $t('删除行') }}
              </mt-button>
            </div>
          </h3>
          <div class="materials-table">
            <vxe-table
              ref="materialTable"
              :data="materialList"
              :edit-config="{ trigger: 'click', mode: 'cell' }"
              :checkbox-config="{ checkField: 'checked' }"
              border
              stripe
              height="400"
            >
              <vxe-column type="checkbox" width="50" fixed="left" />
              <vxe-column type="seq" title="行号" width="80" fixed="left" />
              <vxe-column
                field="materialCode"
                title="物料编码"
                width="150"
                :edit-render="materialCodeEditRender"
              >
                <template #edit="{ row }">
                  <mt-input
                    v-model="row.materialCode"
                    :disabled="!isEditMode"
                    @blur="onMaterialCodeChange(row)"
                  />
                </template>
              </vxe-column>
              <vxe-column field="materialName" title="物料名称" width="200">
                <template #edit="{ row }">
                  <mt-input v-model="row.materialName" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="unit" title="单位" width="100">
                <template #edit="{ row }">
                  <mt-input v-model="row.unit" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column
                field="quantity"
                title="数量"
                width="120"
                :edit-render="quantityEditRender"
              >
                <template #edit="{ row }">
                  <mt-numeric
                    v-model="row.quantity"
                    :disabled="!isEditMode"
                    :min="0"
                    :precision="3"
                  />
                </template>
              </vxe-column>
              <vxe-column
                field="requireDate"
                title="需求日期"
                width="150"
                :edit-render="dateEditRender"
              >
                <template #edit="{ row }">
                  <mt-date-picker
                    v-model="row.requireDate"
                    :disabled="!isEditMode"
                    format="yyyy-MM-dd"
                  />
                </template>
              </vxe-column>
              <vxe-column field="storageLocation" title="库存地点" width="150">
                <template #edit="{ row }">
                  <mt-input v-model="row.storageLocation" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="purchaseGroupCode" title="采购组编码" width="150">
                <template #edit="{ row }">
                  <mt-input v-model="row.purchaseGroupCode" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="purchaseGroupName" title="采购组名称" width="150">
                <template #edit="{ row }">
                  <mt-input v-model="row.purchaseGroupName" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="categoryCode" title="品类编码" width="150">
                <template #edit="{ row }">
                  <mt-input v-model="row.categoryCode" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="categoryName" title="品类名称" width="150">
                <template #edit="{ row }">
                  <mt-input v-model="row.categoryName" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="remark" title="备注" width="200" :edit-render="remarkEditRender">
                <template #edit="{ row }">
                  <mt-textarea v-model="row.remark" :disabled="!isEditMode" :rows="2" />
                </template>
              </vxe-column>
              <vxe-column field="createUserName" title="创建人" width="120">
                <template #edit="{ row }">
                  <mt-input v-model="row.createUserName" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="createTime" title="创建时间" width="150">
                <template #edit="{ row }">
                  <mt-input v-model="row.createTime" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="updateUserName" title="最后更新人" width="120">
                <template #edit="{ row }">
                  <mt-input v-model="row.updateUserName" :disabled="true" />
                </template>
              </vxe-column>
              <vxe-column field="updateTime" title="最后更新时间" width="150">
                <template #edit="{ row }">
                  <mt-input v-model="row.updateTime" :disabled="true" />
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 附件 -->
    <div class="flex-fit" v-show="currentTabIndex === 1">
      <attachment-upload
        ref="attachmentUpload"
        :file-list="attachmentList"
        :disabled="!isEditMode"
        @fileChange="onAttachmentChange"
      />
    </div>
  </div>
</template>

<script>
import TopInfo from './components/TopInfo.vue'
import AttachmentUpload from './components/AttachmentUpload.vue'
import dayjs from 'dayjs'

export default {
  name: 'SubstituteMaterialApplicationDetail',
  components: {
    TopInfo,
    AttachmentUpload
  },
  data() {
    return {
      // 当前Tab索引
      currentTabIndex: 0,
      // Tab列表
      tabList: [
        { text: this.$t('基本信息'), value: 0 },
        { text: this.$t('附件'), value: 1 }
      ],
      // 是否编辑模式
      isEditMode: false,
      // 头部信息
      headerInfo: {
        substituteApplicationNo: '', // 替代料申请单号
        status: '', // 状态
        statusText: '', // 状态文本
        purchaseOrderNo: '', // 采购订单号
        purchaseOrderLineNo: '', // 采购订单行号
        siteCode: '', // 工厂代码
        siteName: '', // 工厂名称
        supplierCode: '', // 加工商编码
        supplierName: '', // 加工商名称
        syncStatus: '', // 同步状态
        syncStatusText: '', // 同步状态文本
        syncMessage: '', // 同步接口信息
        buyerRemark: '', // 采方备注
        supplierRemark: '', // 供方备注
        createUserName: '', // 创建人
        createTime: '', // 创建时间
        updateUserName: '', // 最后更新人
        updateTime: '' // 最后更新时间
      },
      // 头部表单验证规则
      headerRules: {
        purchaseOrderNo: [
          { required: true, message: this.$t('采购订单号不能为空'), trigger: 'blur' }
        ],
        purchaseOrderLineNo: [
          { required: true, message: this.$t('采购订单行号不能为空'), trigger: 'blur' }
        ],
        siteCode: [{ required: true, message: this.$t('工厂代码不能为空'), trigger: 'change' }]
      },
      // 工厂选项
      siteOptions: [],
      // 物料列表
      materialList: [],
      // 附件列表
      attachmentList: [],
      // 表格编辑配置
      materialCodeEditRender: {
        name: 'input',
        props: { placeholder: this.$t('请输入物料编码') }
      },
      quantityEditRender: {
        name: 'input',
        props: { type: 'number', min: 0 }
      },
      dateEditRender: {
        name: 'input',
        props: { type: 'date' }
      },
      remarkEditRender: {
        name: 'textarea',
        props: { placeholder: this.$t('请输入备注') }
      }
    }
  },
  computed: {
    // 申请单ID
    applicationId() {
      return this.$route.params.id || this.$route.query.id
    },
    // 页面模式：view-查看，edit-编辑，add-新增
    pageMode() {
      return this.$route.query.mode || 'view'
    }
  },
  created() {
    this.initPage()
  },
  methods: {
    // 初始化页面
    async initPage() {
      this.isEditMode = this.pageMode === 'edit' || this.pageMode === 'add'

      // 加载工厂选项
      await this.loadSiteOptions()

      if (this.pageMode === 'add') {
        // 新增模式：初始化默认数据
        this.initDefaultData()
      } else {
        // 查看/编辑模式：加载详情数据
        await this.loadDetailData()
      }
    },

    // 加载工厂选项
    async loadSiteOptions() {
      try {
        const res = await this.$API.masterData.getSiteList({
          BU_CODE: localStorage.getItem('currentBu')
        })
        if (res?.code === 200) {
          this.siteOptions = res.data?.records || []
        }
      } catch (error) {
        console.error('加载工厂选项失败:', error)
      }
    },

    // 初始化默认数据
    initDefaultData() {
      // 生成申请单号
      this.headerInfo.substituteApplicationNo = this.generateApplicationNo()
      this.headerInfo.status = '0' // 待处理
      this.headerInfo.statusText = this.$t('待处理')
      this.headerInfo.syncStatus = '0' // 未同步
      this.headerInfo.syncStatusText = this.$t('未同步')
      this.headerInfo.createUserName = this.$store.state.user.username
      this.headerInfo.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

      // 添加一行默认物料
      this.addMaterialRow()
    },

    // 生成申请单号
    generateApplicationNo() {
      const now = dayjs()
      const dateStr = now.format('YYYYMMDD')
      const timeStr = now.format('HHmmss')
      return `TDL${dateStr}${timeStr}`
    },

    // 加载详情数据
    async loadDetailData() {
      try {
        const res = await this.$API.outsourcingNew.getSubstituteMaterialApplicationDetailById({
          id: this.applicationId
        })
        if (res?.code === 200) {
          const data = res.data
          // 设置头部信息
          this.headerInfo = {
            ...data,
            statusText: this.getStatusText(data.status),
            syncStatusText: this.getSyncStatusText(data.syncStatus)
          }
          // 设置物料列表
          this.materialList = data.materialList || []
          // 设置附件列表
          this.attachmentList = data.attachmentList || []
        }
      } catch (error) {
        console.error('加载详情数据失败:', error)
        this.$toast({
          content: this.$t('加载数据失败'),
          type: 'error'
        })
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: this.$t('待处理'),
        1: this.$t('已处理'),
        2: this.$t('已退回')
      }
      return statusMap[status] || status
    },

    // 获取同步状态文本
    getSyncStatusText(syncStatus) {
      const syncStatusMap = {
        0: this.$t('未同步'),
        1: this.$t('同步成功'),
        2: this.$t('同步失败')
      }
      return syncStatusMap[syncStatus] || syncStatus
    },

    // Tab切换
    handleSelectTab(tab) {
      this.currentTabIndex = tab.value
    },

    // 工厂变更
    onSiteChange(siteCode) {
      const site = this.siteOptions.find((item) => item.siteCode === siteCode)
      if (site) {
        this.headerInfo.siteName = site.siteName
      }
    },

    // 新增物料行
    addMaterialRow() {
      const newRow = {
        id: null,
        lineNo: this.materialList.length + 1,
        materialCode: '',
        materialName: '',
        unit: '',
        quantity: 0,
        requireDate: '',
        storageLocation: '',
        purchaseGroupCode: '',
        purchaseGroupName: '',
        categoryCode: '',
        categoryName: '',
        remark: '',
        createUserName: this.$store.state.user.username,
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updateUserName: '',
        updateTime: '',
        checked: false
      }
      this.materialList.push(newRow)
    },

    // 删除物料行
    deleteMaterialRows() {
      const checkedRows = this.materialList.filter((row) => row.checked)
      if (checkedRows.length === 0) {
        this.$toast({
          content: this.$t('请选择要删除的行'),
          type: 'warning'
        })
        return
      }

      this.$dialog.confirm({
        title: this.$t('确认删除'),
        content: this.$t('确定要删除选中的{count}行数据吗？', { count: checkedRows.length }),
        onOk: () => {
          this.materialList = this.materialList.filter((row) => !row.checked)
          // 重新设置行号
          this.materialList.forEach((row, index) => {
            row.lineNo = index + 1
          })
        }
      })
    },

    // 物料编码变更
    async onMaterialCodeChange(row) {
      if (!row.materialCode) {
        return
      }

      try {
        // 调用SAP接口获取物料信息
        const res = await this.$API.outsourcingNew.getMaterialInfo({
          materialCode: row.materialCode,
          siteCode: this.headerInfo.siteCode
        })

        if (res?.code === 200) {
          const materialInfo = res.data
          // 自动填充物料信息
          row.materialName = materialInfo.materialName || ''
          row.unit = materialInfo.unit || ''
          row.storageLocation = materialInfo.storageLocation || ''
          row.purchaseGroupCode = materialInfo.purchaseGroupCode || ''
          row.purchaseGroupName = materialInfo.purchaseGroupName || ''
          row.categoryCode = materialInfo.categoryCode || ''
          row.categoryName = materialInfo.categoryName || ''
        }
      } catch (error) {
        console.error('获取物料信息失败:', error)
      }
    },

    // 附件变更
    onAttachmentChange(fileList) {
      this.attachmentList = fileList
    },

    // 返回
    goBack() {
      this.$router.go(-1)
    },

    // 获取物料信息
    async getMaterialInfo() {
      if (!this.headerInfo.purchaseOrderNo || !this.headerInfo.purchaseOrderLineNo) {
        this.$toast({
          content: this.$t('请先填写采购订单号和行号'),
          type: 'warning'
        })
        return
      }

      try {
        const res = await this.$API.outsourcingNew.getMaterialInfoByPO({
          purchaseOrderNo: this.headerInfo.purchaseOrderNo,
          purchaseOrderLineNo: this.headerInfo.purchaseOrderLineNo,
          siteCode: this.headerInfo.siteCode
        })

        if (res?.code === 200) {
          const materialInfo = res.data
          // 自动填充加工商信息
          this.headerInfo.supplierCode = materialInfo.supplierCode || ''
          this.headerInfo.supplierName = materialInfo.supplierName || ''

          this.$toast({
            content: this.$t('获取物料信息成功'),
            type: 'success'
          })
        }
      } catch (error) {
        console.error('获取物料信息失败:', error)
        this.$toast({
          content: this.$t('获取物料信息失败'),
          type: 'error'
        })
      }
    },

    // 保存
    async doSave() {
      try {
        // 验证表单
        const isValid = await this.validateForm()
        if (!isValid) {
          return
        }

        const params = {
          ...this.headerInfo,
          materialList: this.materialList,
          attachmentList: this.attachmentList
        }

        let res
        if (this.pageMode === 'add') {
          res = await this.$API.outsourcingNew.createSubstituteMaterialApplication(params)
        } else {
          res = await this.$API.outsourcingNew.updateSubstituteMaterialApplication(params)
        }

        if (res?.code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })

          if (this.pageMode === 'add') {
            // 新增成功后跳转到编辑页面
            this.$router.replace({
              path: `/outsourcingNew/supplier/substituteMaterialApplication/detail/${res.data.id}`,
              query: { mode: 'edit' }
            })
          }
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$toast({
          content: this.$t('保存失败'),
          type: 'error'
        })
      }
    },

    // 提交
    async doSubmit() {
      try {
        // 先保存
        await this.doSave()

        // 再提交
        const res = await this.$API.outsourcingNew.submitSubstituteMaterialApplication({
          id: this.applicationId
        })

        if (res?.code === 200) {
          this.$toast({
            content: this.$t('提交成功'),
            type: 'success'
          })

          // 刷新页面数据
          await this.loadDetailData()
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$toast({
          content: this.$t('提交失败'),
          type: 'error'
        })
      }
    },

    // 验证表单
    async validateForm() {
      try {
        // 验证头部表单
        await this.$refs.headerForm.validate()

        // 验证物料列表
        if (this.materialList.length === 0) {
          this.$toast({
            content: this.$t('请至少添加一行物料信息'),
            type: 'warning'
          })
          return false
        }

        // 验证必填字段
        for (let i = 0; i < this.materialList.length; i++) {
          const row = this.materialList[i]
          if (!row.materialCode) {
            this.$toast({
              content: this.$t('第{line}行物料编码不能为空', { line: i + 1 }),
              type: 'warning'
            })
            return false
          }
          if (!row.quantity || row.quantity <= 0) {
            this.$toast({
              content: this.$t('第{line}行数量必须大于0', { line: i + 1 }),
              type: 'warning'
            })
            return false
          }
          if (!row.requireDate) {
            this.$toast({
              content: this.$t('第{line}行需求日期不能为空', { line: i + 1 }),
              type: 'warning'
            })
            return false
          }
        }

        return true
      } catch (error) {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-page {
  padding: 20px;
  background-color: #f5f5f5;
}

.detail-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-actions {
  display: flex;
  gap: 10px;
}

.header-form {
  .mt-row {
    margin-bottom: 16px;
  }
}

.detail-materials {
  margin-top: 30px;
}

.materials-table {
  margin-top: 20px;

  ::v-deep .vxe-table {
    .vxe-header--column {
      background-color: #f8f9fa;
      font-weight: bold;
    }

    .vxe-cell {
      padding: 8px;
    }

    .vxe-input,
    .vxe-textarea {
      width: 100%;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 4px 8px;

      &:focus {
        border-color: #409eff;
        outline: none;
      }
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .mt-col {
    &[span='8'] {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }
}

@media (max-width: 768px) {
  .detail-page {
    padding: 10px;
  }

  .mt-col {
    &[span='8'] {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  .section-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .section-actions {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
