<!-- 替代料申请管理详情页-附件上传组件 -->
<template>
  <div class="attachment-upload">
    <div class="upload-header">
      <h3 class="section-title">{{ $t('附件字段') }}</h3>
      <mt-button 
        v-if="!disabled" 
        size="small" 
        css-class="e-flat" 
        @click="handleUpload"
      >
        {{ $t('上传文件') }}
      </mt-button>
    </div>
    
    <!-- 文件列表 -->
    <div class="file-list">
      <vxe-table
        :data="fileList"
        border
        stripe
        height="400"
      >
        <vxe-column type="seq" title="序号" width="80" />
        <vxe-column field="fileName" title="文件名" min-width="200">
          <template #default="{ row }">
            <a @click="previewFile(row)" class="file-link">
              {{ row.fileName }}
            </a>
          </template>
        </vxe-column>
        <vxe-column field="fileType" title="文件类型" width="120" />
        <vxe-column field="fileSize" title="文件大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </vxe-column>
        <vxe-column field="createUserName" title="创建人" width="120" />
        <vxe-column field="createTime" title="创建时间" width="150">
          <template #default="{ row }">
            {{ row.createTime | dateFormat }}
          </template>
        </vxe-column>
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row }">
            <mt-button 
              size="small" 
              css-class="e-flat" 
              @click="downloadFile(row)"
            >
              {{ $t('下载') }}
            </mt-button>
            <mt-button 
              v-if="!disabled" 
              size="small" 
              css-class="e-flat" 
              @click="deleteFile(row)"
            >
              {{ $t('删除') }}
            </mt-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    
    <!-- 上传对话框 -->
    <mt-dialog
      ref="uploadDialog"
      :width="600"
      :height="400"
      :header="$t('上传文件')"
      :buttons="uploadDialogButtons"
      @beforeClose="handleDialogClose"
    >
      <div class="upload-content">
        <div class="upload-area" @drop="handleDrop" @dragover="handleDragOver">
          <input
            ref="fileInput"
            type="file"
            multiple
            style="display: none"
            @change="handleFileSelect"
          />
          <div class="upload-placeholder" @click="selectFiles">
            <i class="mt-icon-upload upload-icon"></i>
            <p>{{ $t('点击或拖拽文件到此处上传') }}</p>
            <p class="upload-tip">{{ $t('支持多文件上传，单个文件不超过50MB') }}</p>
          </div>
        </div>
        
        <!-- 待上传文件列表 -->
        <div v-if="pendingFiles.length > 0" class="pending-files">
          <h4>{{ $t('待上传文件') }}</h4>
          <div v-for="(file, index) in pendingFiles" :key="index" class="pending-file">
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
            <mt-button 
              size="small" 
              css-class="e-flat" 
              @click="removePendingFile(index)"
            >
              {{ $t('移除') }}
            </mt-button>
          </div>
        </div>
      </div>
    </mt-dialog>
  </div>
</template>

<script>
export default {
  name: 'AttachmentUpload',
  props: {
    // 文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 待上传文件
      pendingFiles: [],
      // 上传对话框按钮
      uploadDialogButtons: [
        {
          text: this.$t('确定'),
          click: this.confirmUpload
        },
        {
          text: this.$t('取消'),
          click: this.cancelUpload
        }
      ]
    }
  },
  filters: {
    // 日期格式化
    dateFormat(value) {
      if (!value) return ''
      return value.replace(/T/, ' ').replace(/\.\d{3}Z$/, '')
    }
  },
  methods: {
    // 打开上传对话框
    handleUpload() {
      this.pendingFiles = []
      this.$refs.uploadDialog.show()
    },
    
    // 选择文件
    selectFiles() {
      this.$refs.fileInput.click()
    },
    
    // 文件选择处理
    handleFileSelect(event) {
      const files = Array.from(event.target.files)
      this.addFiles(files)
    },
    
    // 拖拽处理
    handleDragOver(event) {
      event.preventDefault()
    },
    
    handleDrop(event) {
      event.preventDefault()
      const files = Array.from(event.dataTransfer.files)
      this.addFiles(files)
    },
    
    // 添加文件
    addFiles(files) {
      files.forEach(file => {
        // 检查文件大小
        if (file.size > 50 * 1024 * 1024) {
          this.$toast({
            content: this.$t('文件 {name} 超过50MB限制', { name: file.name }),
            type: 'warning'
          })
          return
        }
        
        // 检查是否已存在
        const exists = this.pendingFiles.some(f => f.name === file.name && f.size === file.size)
        if (!exists) {
          this.pendingFiles.push(file)
        }
      })
    },
    
    // 移除待上传文件
    removePendingFile(index) {
      this.pendingFiles.splice(index, 1)
    },
    
    // 确认上传
    async confirmUpload() {
      if (this.pendingFiles.length === 0) {
        this.$toast({
          content: this.$t('请选择要上传的文件'),
          type: 'warning'
        })
        return
      }
      
      try {
        const uploadPromises = this.pendingFiles.map(file => this.uploadSingleFile(file))
        const results = await Promise.all(uploadPromises)
        
        // 更新文件列表
        const newFileList = [...this.fileList, ...results]
        this.$emit('fileChange', newFileList)
        
        this.$toast({
          content: this.$t('文件上传成功'),
          type: 'success'
        })
        
        this.$refs.uploadDialog.hide()
      } catch (error) {
        console.error('文件上传失败:', error)
        this.$toast({
          content: this.$t('文件上传失败'),
          type: 'error'
        })
      }
    },
    
    // 上传单个文件
    async uploadSingleFile(file) {
      const formData = new FormData()
      formData.append('file', file)
      
      const res = await this.$API.fileService.uploadFile(formData)
      if (res?.code === 200) {
        return {
          id: res.data.id,
          fileName: file.name,
          fileType: this.getFileType(file.name),
          fileSize: file.size,
          filePath: res.data.filePath,
          createUserName: this.$store.state.user.username,
          createTime: new Date().toISOString()
        }
      }
      throw new Error('上传失败')
    },
    
    // 获取文件类型
    getFileType(fileName) {
      const ext = fileName.split('.').pop().toLowerCase()
      const typeMap = {
        'pdf': 'PDF',
        'doc': 'Word',
        'docx': 'Word',
        'xls': 'Excel',
        'xlsx': 'Excel',
        'ppt': 'PowerPoint',
        'pptx': 'PowerPoint',
        'jpg': '图片',
        'jpeg': '图片',
        'png': '图片',
        'gif': '图片',
        'zip': '压缩包',
        'rar': '压缩包'
      }
      return typeMap[ext] || '其他'
    },
    
    // 取消上传
    cancelUpload() {
      this.$refs.uploadDialog.hide()
    },
    
    // 对话框关闭处理
    handleDialogClose() {
      this.pendingFiles = []
    },
    
    // 预览文件
    previewFile(file) {
      // 根据文件类型进行预览
      if (file.filePath) {
        window.open(file.filePath, '_blank')
      }
    },
    
    // 下载文件
    downloadFile(file) {
      if (file.filePath) {
        const link = document.createElement('a')
        link.href = file.filePath
        link.download = file.fileName
        link.click()
      }
    },
    
    // 删除文件
    deleteFile(file) {
      this.$dialog.confirm({
        title: this.$t('确认删除'),
        content: this.$t('确定要删除文件 {name} 吗？', { name: file.fileName }),
        onOk: () => {
          const newFileList = this.fileList.filter(f => f.id !== file.id)
          this.$emit('fileChange', newFileList)
        }
      })
    },
    
    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(1)} ${units[index]}`
    }
  }
}
</script>

<style lang="scss" scoped>
.attachment-upload {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.file-list {
  margin-top: 20px;
}

.file-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

.upload-content {
  padding: 20px;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 4px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  
  &:hover {
    border-color: #1890ff;
  }
}

.upload-placeholder {
  .upload-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }
  
  p {
    margin: 8px 0;
    color: #666;
  }
  
  .upload-tip {
    font-size: 12px;
    color: #999;
  }
}

.pending-files {
  margin-top: 20px;
  
  h4 {
    margin-bottom: 10px;
    color: #333;
  }
}

.pending-file {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  .file-name {
    flex: 1;
    color: #333;
  }
  
  .file-size {
    margin-right: 10px;
    color: #999;
    font-size: 12px;
  }
}
</style>
